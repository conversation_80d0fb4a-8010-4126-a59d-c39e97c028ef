<!-- 设计暴雨 -->
<template>
  <div class="design-storm-container">
    <!-- 左侧区域 -->
    <div class="left-section">
      <!-- 上半部分：桃花江水库设计洪水表 -->
      <div class="top-section">
        <div class="section-header">
          <div class="title-row">
            <h3 class="section-title">桃花江水库设计洪水表</h3>
          </div>
          <div class="header-row">
            <div class="unit-text">单位：m³/s</div>
            <a-button type="primary" size="small" icon="plus" @click="handleAdd">添加</a-button>
          </div>
        </div>
        <div class="table-container">
          <vxe-table
            ref="floodTableRef"
            :data="floodTableData"
            border
            size="small"
            :merge-cells="mergeCells"
            :column-config="{ resizable: true }"
            :auto-resize="true"
            :show-overflow="false"
            :stripe="false"
            max-height="300px"
          >
            <vxe-column field="stage" title="阶段" width="18%"></vxe-column>
            <vxe-column field="peakVolume" title="峰量" width="12%"></vxe-column>
            <vxe-column field="p333" title="P=3.33%" width="18%"></vxe-column>
            <vxe-column field="p2" title="P=2%" width="18%"></vxe-column>
            <vxe-column field="p02" title="P=0.2%" width="18%"></vxe-column>
            <vxe-column title="操作" width="16%">
              <template #default="{ row }">
                <a-button type="link" size="small" @click="handleEdit(row)">编辑</a-button>
                <a-divider type="vertical" />
                <a-button type="link" size="small" danger @click="handleDelete(row)">删除</a-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>

      <!-- 下半部分：桃花江水库各频率设计洪水过程表 -->
      <div class="bottom-section">
        <div class="section-header">
          <h3 class="section-title">桃花江水库各频率设计洪水过程表</h3>
        </div>
        <div class="table-container">
          <vxe-table
            ref="processTableRef"
            :data="processTableData"
            border
            size="small"
            height="auto"
            max-height="300px"
            :column-config="{ resizable: true }"
            :auto-resize="true"
          >
            <vxe-column field="timeSlot" title="时段（1h）" width="25%"></vxe-column>
            <vxe-column field="p333" title="P=3.33%" width="25%"></vxe-column>
            <vxe-column field="p2" title="P=2%" width="25%"></vxe-column>
            <vxe-column field="p02" title="P=0.2%" width="25%"></vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="right-section">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div>洪水过程线:</div>
        <a-select
          v-model="selectedStage"
          placeholder="请选择阶段"
          style="width: 200px; margin-right: 8px"
          @change="handleStageChange"
        >
          <a-select-option value="2024">2024年</a-select-option>
          <a-select-option value="2019">2019年加固</a-select-option>
          <a-select-option value="1966">1966年初设</a-select-option>
        </a-select>
        <a-button type="default" icon="upload" @click="handleUpload">上传</a-button>
        <a-button type="default" icon="download" @click="handleDownload">下载</a-button>
        <a-button type="primary" @click="handleSetDefault">设为默认</a-button>
      </div>

      <!-- 图表区域 -->
      <div class="chart-container">
        <div class="chart-title">桃花江水库各频率设计洪水过程线</div>
        <div class="chart-content">
          <LineEchart
            :dataSource="chartData.dataSource"
            :custom="chartData.custom"
            height="100%"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { LineEchart } from '@/components/Echarts'

export default {
  name: 'DesignStorm',
  components: {
    LineEchart
  },
  data() {
    return {
      selectedStage: '2024',

      // 设计洪水表数据
      floodTableData: [
        { stage: '2024年', peakVolume: '洪峰', p333: 1017, p2: 661, p02: 605 },
        { stage: '2024年', peakVolume: '洪量', p333: 3974, p2: 2748, p02: 2470 },
        { stage: '2019年加固', peakVolume: '洪峰', p333: 1260, p2: 859, p02: 739 },
        { stage: '2019年加固', peakVolume: '洪量', p333: 6440, p2: 4240, p02: 3750 },
        { stage: '1966年初设', peakVolume: '洪峰', p333: 1260, p2: 859, p02: 739 },
        { stage: '1966年初设', peakVolume: '洪量', p333: 6440, p2: 4240, p02: 3750 }
      ],

      // VxeTable合并单元格配置
      mergeCells: [
        { row: 0, col: 0, rowspan: 2, colspan: 1 }, // 2024年
        { row: 2, col: 0, rowspan: 2, colspan: 1 }, // 2019年加固
        { row: 4, col: 0, rowspan: 2, colspan: 1 }  // 1966年初设
      ],

      // 洪水过程表数据
      processTableData: [
        { key: '1', timeSlot: 0, p333: 120, p2: 80, p02: 60 },
        { key: '2', timeSlot: 1, p333: 180, p2: 120, p02: 90 },
        { key: '3', timeSlot: 2, p333: 250, p2: 170, p02: 130 },
        { key: '4', timeSlot: 3, p333: 320, p2: 220, p02: 170 },
        { key: '5', timeSlot: 4, p333: 400, p2: 280, p02: 220 },
        { key: '6', timeSlot: 5, p333: 480, p2: 340, p02: 270 },
        { key: '7', timeSlot: 6, p333: 560, p2: 400, p02: 320 },
        { key: '8', timeSlot: 7, p333: 620, p2: 450, p02: 360 },
        { key: '9', timeSlot: 8, p333: 680, p2: 500, p02: 400 },
        { key: '10', timeSlot: 9, p333: 720, p2: 530, p02: 420 },
        { key: '11', timeSlot: 10, p333: 650, p2: 480, p02: 380 }
      ]
    }
  },

  computed: {
    // 图表数据配置
    chartData() {
      return {
        dataSource: [
          {
            name: 'P=3.33%',
            color: '#1890ff',
            data: this.processTableData.map(item => [item.timeSlot, item.p333])
          },
          {
            name: 'P=2%',
            color: '#52c41a',
            data: this.processTableData.map(item => [item.timeSlot, item.p2])
          },
          {
            name: 'P=0.2%',
            color: '#fa8c16',
            data: this.processTableData.map(item => [item.timeSlot, item.p02])
          }
        ],
        custom: {
          xLabel: 'T(h)',
          yLabel: 'Q（m³/s）',
          legend: true,
          legendOptions: {
            top: 30,
            left: 'center'
          },
          showAreaStyle: false,
          dataZoom: true,
          grid: {
            top: 80,
            left: 60,
            right: 40,
            bottom: 80
          }
        }
      }
    }
  },

  methods: {
    // 阶段选择变化
    handleStageChange(value) {
      this.$message.info(`选择了阶段: ${value}`)
      // 这里可以根据选择的阶段更新图表数据
    },

    // 添加数据
    handleAdd() {
      this.$message.info('添加功能待实现')
    },

    // 编辑数据
    handleEdit(record) {
      this.$message.info(`编辑数据: ${record.stage}`)
    },

    // 删除数据
    handleDelete(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除 ${record.stage} 的数据吗？`,
        onOk: () => {
          this.$message.success('删除成功')
        }
      })
    },

    // 上传文件
    handleUpload() {
      this.$message.info('上传功能待实现')
    },

    // 下载文件
    handleDownload() {
      this.$message.info('下载功能待实现')
    },

    // 设为默认
    handleSetDefault() {
      this.$message.success('已设为默认')
    }
  }
}
</script>

<style lang="less" scoped>
.design-storm-container {
  height: 100vh;
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: #fff;
  box-sizing: border-box;

  .left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .top-section {
      background: white;
      border-radius: 6px;
      padding: 16px;
      display: flex;
      flex-direction: column;
    //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
     .section-header {
        width: 100%;
        margin-bottom: 16px;

        .title-row {
          margin-bottom: 8px;
        }

        .section-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .header-row {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          

          .unit-text {
            color: #666;
            font-size: 14px;
            flex-shrink: 0;
          }
        }
      }
    }

    .bottom-section {
      flex: 1;
      background: white;
      border-radius: 6px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      min-height: 0;
    //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .section-header {
        width: 100%;
        margin-bottom: 16px;

        .title-row {
          margin-bottom: 8px;
        }

        .section-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .header-row {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          

          .unit-text {
            color: #666;
            font-size: 14px;
            flex-shrink: 0;
          }
        }
      }

      .table-container {
        flex: 1;
        overflow: hidden;
      }
    }
  }

  .right-section {
    flex: 1;
    background: white;
    border-radius: 6px;
    padding: 16px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;

    .operation-bar {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      gap: 8px;
    }

    .chart-container {
      flex: 1;
      min-height: 400px;
      display: flex;
      flex-direction: column;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        text-align: center;
        margin-bottom: 16px;
        padding: 8px 0;
      }

      .chart-content {
        flex: 1;
        min-height: 350px;
      }
    }
  }
}

// 隐藏VxeTable的空行
::v-deep .vxe-table--render-default {
  .vxe-body--row:empty {
    display: none;
  }

  .vxe-body--row {
    &:last-child {
      .vxe-body--column:empty {
        display: none;
      }
    }
  }
}

// 确保表格高度适应内容
::v-deep .vxe-table--body-wrapper {
  overflow: hidden !important;
}
</style>